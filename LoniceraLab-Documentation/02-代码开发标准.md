# 🏢 LoniceraLab 代码开发标准

<!-- Copyright (c) 2025 LoniceraLab. All rights reserved. -->

## 📋 项目信息
- **版权方**: LoniceraLab
- **版权年份**: 2025
- **开发平台**: iOS 平台
- **开发语言**: Swift 语言（全程使用）
- **UI 框架**: SwiftUI
- **项目性质**: 高效、精简、中文友好的现代 iOS 应用
- **开发阶段**: 框架搭建期
- **最低支持版本**: iOS 15.0+
- **开发工具**: Xcode 15.0+

---

## 📜 LoniceraLab 版权水印标准

### 1. 强制版权声明格式（严格执行）

#### Swift 文件版权格式（必须完全一致）
```swift
// Copyright (c) 2025 LoniceraLab. All rights reserved.
```

**格式要求**：
- ✅ 必须使用双斜杠注释 `//`
- ✅ `Copyright` 首字母大写，其余小写
- ✅ `(c)` 必须使用小括号包围小写字母 c
- ✅ 年份必须是 `2025`
- ✅ 公司名必须是 `LoniceraLab`（无空格，L 和 L 大写）
- ✅ 必须以 `All rights reserved.` 结尾
- ✅ 句号不能省略
- ✅ 单词间用单个空格分隔

#### 错误格式示例（禁止使用）
```swift
// ❌ 错误：使用了多行注释
/*
 * Copyright (c) 2025 LoniceraLab. All rights reserved.
 */

// ❌ 错误：缺少 (c)
// Copyright 2025 LoniceraLab. All rights reserved.

// ❌ 错误：公司名格式错误
// Copyright (c) 2025 Lonicera Lab. All rights reserved.

// ❌ 错误：缺少句号
// Copyright (c) 2025 LoniceraLab. All rights reserved

// ❌ 错误：年份错误
// Copyright (c) 2024 LoniceraLab. All rights reserved.

// ❌ 错误：大小写错误
// copyright (c) 2025 loniceralab. all rights reserved.
```

### 2. 版权声明位置要求（严格执行）
- **Swift 文件**: 文件第一行，在所有 import 语句之前（项目主要文件类型）
- **配置文件**: 文件顶部第一行（如 Info.plist、配置 JSON 等）
- **脚本文件**: shebang 之后的第一行（构建脚本、测试脚本等）
- **文档文件**: 文件开头或元数据区域（README、技术文档等）

### 3. 版权声明检查清单
在创建或修改任何文件时，必须检查：
- [ ] 版权声明位于文件第一行（Swift 文件）
- [ ] 格式完全符合标准模板
- [ ] 没有多余的空行或字符
- [ ] 大小写完全正确
- [ ] 标点符号完全正确
- [ ] 年份是 2025
- [ ] 公司名是 LoniceraLab

### 4. 标准版权声明示例

#### Swift 代码文件（标准模板）
```swift
// Copyright (c) 2025 LoniceraLab. All rights reserved.

import SwiftUI
import Foundation
import UIKit

@MainActor
class ExampleViewModel: ObservableObject {
    @Published var isLoading = false // 加载状态
    
    private let service: ExampleServiceProtocol
    
    init(service: ExampleServiceProtocol) {
        self.service = service
    }
    
    // iOS 应用业务逻辑实现...
}
```

#### SwiftUI View 文件示例
```swift
// Copyright (c) 2025 LoniceraLab. All rights reserved.

import SwiftUI

struct ContentView: View {
    @StateObject private var viewModel = ContentViewModel()
    
    var body: some View {
        NavigationView {
            VStack {
                Text("LoniceraLab iOS App")
                    .font(.title)
            }
            .navigationTitle("主页")
        }
    }
}
```

#### Service 文件示例
```swift
// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation

protocol DataServiceProtocol: Actor {
    func fetchData() async throws -> [DataModel]
}

actor DataService: DataServiceProtocol {
    func fetchData() async throws -> [DataModel] {
        // iOS 数据服务实现
        return []
    }
}
```

#### Model 文件示例
```swift
// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation

struct UserModel: Codable, Identifiable {
    let id: UUID = UUID()
    let name: String // 用户姓名
    let email: String // 用户邮箱
    
    var isValid: Bool {
        !name.isEmpty && email.contains("@")
    }
}
```

---

## ⚡ iOS Swift 效率与性能原则

### 1. iOS 平台性能优先选择
```swift
// ✅ 优先选择 iOS 高性能方案
actor DataService { // Swift Actor 确保并发安全
    private var cache: [String: Data] = [:] // 内存缓存提升 iOS 应用性能
    
    func getData(key: String) async -> Data? {
        return cache[key] // O(1) 查找，适合 iOS 快速响应需求
    }
}

// ❌ 避免 iOS 低性能方案
class DataService {
    func getData(key: String) -> Data? {
        // 每次都从磁盘读取，在 iOS 设备上性能差
        return try? Data(contentsOf: URL(fileURLWithPath: key))
    }
}
```

### 2. iOS Swift 异步优化策略
```swift
// ✅ 使用 Swift 现代异步模式优化 iOS 应用性能
@MainActor
class ImageProcessor: ObservableObject {
    @Published var processedImage: UIImage? // SwiftUI 响应式更新
    @Published var isProcessing = false // iOS 用户体验指示器
    
    func processImage(_ image: UIImage) async {
        isProcessing = true
        defer { isProcessing = false }
        
        let processed = await withTaskGroup(of: UIImage?.self) { group in
            group.addTask { await self.applyFilter(image) } // iOS 图像处理并行优化
            group.addTask { await self.adjustColors(image) } // iOS 颜色调整并行优化
            
            var results: [UIImage] = []
            for await result in group {
                if let img = result { results.append(img) }
            }
            return results.first
        }
        processedImage = processed
    }
}
```

### 3. iOS 内存管理优化
```swift
// ✅ iOS 设备高效内存管理
class ImageCache {
    private let cache = NSCache<NSString, UIImage>() // iOS 系统自动内存管理
    
    init() {
        cache.countLimit = 50 // 限制缓存数量，适合 iOS 设备内存限制
        cache.totalCostLimit = 100 * 1024 * 1024 // 限制内存使用 100MB，考虑 iOS 内存压力
        
        // iOS 内存警告处理
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.cache.removeAllObjects() // iOS 内存警告时清理缓存
        }
    }
    
    func setImage(_ image: UIImage, forKey key: String) {
        let cost = image.jpegData(compressionQuality: 1.0)?.count ?? 0
        cache.setObject(image, forKey: key as NSString, cost: cost)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self) // Swift ARC 内存管理
    }
}
```

---

## 🎯 Swift iOS 精简主义原则

### 1. Swift 语言最少代码行实现
```swift
// ✅ Swift 精简实现（1行）- 利用 Swift 语言特性
func calculateAverage(_ numbers: [Double]) -> Double {
    numbers.isEmpty ? 0 : numbers.reduce(0, +) / Double(numbers.count)
}

// ✅ iOS 应用场景的精简实现
extension Array where Element == Double {
    var average: Double { isEmpty ? 0 : reduce(0, +) / Double(count) } // Swift 计算属性
}

// 使用：iOS 应用中的简洁调用
let scores = [85.5, 92.0, 78.5, 96.0]
let avgScore = scores.average // Swift 语法糖

// ❌ 冗长实现（不符合 Swift 最佳实践）
func calculateAverage(_ numbers: [Double]) -> Double {
    if numbers.isEmpty {
        return 0.0
    }
    
    var sum: Double = 0.0
    for number in numbers {
        sum += number
    }
    
    return sum / Double(numbers.count)
}
```

### 2. Swift iOS 避免过度抽象
```swift
// ✅ Swift 直接简洁实现 - 适合 iOS 应用快速开发
struct UserProfile: Codable, Identifiable { // Swift 协议组合
    let id = UUID() // iOS 应用常用标识符
    let name: String // 直接属性
    let email: String // 直接属性
    
    var displayName: String { name.isEmpty ? email : name } // Swift 计算属性
    var isValid: Bool { !name.isEmpty && email.contains("@") } // iOS 表单验证
}

// ✅ iOS SwiftUI 中的使用
struct ProfileView: View {
    let profile: UserProfile
    
    var body: some View {
        VStack {
            Text(profile.displayName) // 直接使用，无需复杂抽象
                .font(.headline)
            Text(profile.email)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

// ❌ 过度抽象（不适合 iOS 快速开发）
protocol Nameable {
    var name: String { get }
}

protocol Emailable {
    var email: String { get }
}

struct UserProfile: Nameable, Emailable {
    let name: String
    let email: String
    
    var displayName: String {
        let nameProvider: Nameable = self
        let emailProvider: Emailable = self
        return nameProvider.name.isEmpty ? emailProvider.email : nameProvider.name
    }
}
```

### 3. Swift iOS 消除重复代码
```swift
// ✅ SwiftUI 统一处理 - iOS 应用样式一致性
extension View {
    func standardButton() -> some View { // iOS 应用统一按钮样式
        self
            .foregroundColor(.white)
            .background(AppConfig.primaryColor)
            .cornerRadius(AppConfig.buttonCornerRadius)
            .padding(AppConfig.buttonPadding)
            .shadow(radius: 2) // iOS 视觉效果
    }
    
    func primaryCard() -> some View { // iOS 卡片样式复用
        self
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            .padding(.horizontal)
    }
}

// iOS SwiftUI 中的使用
struct ActionButtonsView: View {
    let onSave: () -> Void
    let onCancel: () -> Void
    
    var body: some View {
        HStack {
            Button("保存") { onSave() }.standardButton() // 复用样式
            Button("取消") { onCancel() }.standardButton() // 复用样式
        }
        .primaryCard() // iOS 卡片样式复用
    }
}

// ✅ Swift 枚举消除重复常量
enum AppConstants {
    enum UI {
        static let cornerRadius: CGFloat = 12 // iOS 设计规范
        static let standardPadding: CGFloat = 16 // iOS 间距标准
        static let animationDuration: Double = 0.3 // iOS 动画时长
    }
    
    enum Colors {
        static let primary = Color.blue // iOS 主题色
        static let secondary = Color.gray // iOS 次要色
        static let background = Color(.systemBackground) // iOS 系统背景色
    }
}
```

---

## 📝 注释规范

### 1. 右侧注释格式
```swift
// ✅ 正确的注释格式
let maxRetryCount = 3 // 最大重试次数
var isLoading = false // 是否正在加载
let apiEndpoint = "https://api.example.com" // API 接口地址

// ❌ 错误的注释格式
/*
 * 多行注释不符合规范
 */
let maxRetryCount = 3

// 这是一个很长的注释，解释了这个变量的用途和背景
// 应该控制在一行以内
let isLoading = false
```

### 2. 函数注释规范
```swift
// ✅ 一行函数注释
// 计算图片滤镜效果并返回处理结果
func applyFilter(to image: UIImage, with parameters: FilterParameters) -> UIImage {
    // 实现代码...
}

// ✅ 类注释
// 图片处理服务，提供滤镜和编辑功能
actor ImageProcessingService {
    // 实现代码...
}
```

### 3. 复杂逻辑注释
```swift
func complexCalculation(input: Double) -> Double {
    let step1 = input * 0.8 // 应用基础倍数
    let step2 = step1 + 0.2 // 添加偏移量
    let result = max(0, min(1, step2)) // 限制在 0-1 范围内
    return result
}
```

---

## 🏗️ Swift iOS 配置中心原则

### 1. iOS 应用统一配置管理
```swift
// ✅ iOS 应用集中配置管理
struct AppConfig {
    // iOS UI 配置
    static let primaryColor = Color.blue // iOS 主题色
    static let secondaryColor = Color(.systemGray) // iOS 系统灰色
    static let cornerRadius: CGFloat = 12 // iOS 设计规范圆角
    static let standardPadding: CGFloat = 16 // iOS 标准内边距
    
    // iOS 设备适配配置
    static let maxImageSize: CGFloat = UIScreen.main.bounds.width * 2 // 基于 iOS 屏幕尺寸
    static let compressionQuality: CGFloat = 0.8 // iOS 图片压缩质量
    static let maxRetryCount = 3 // iOS 网络重试次数
    
    // iOS 动画配置
    static let standardAnimationDuration: Double = 0.3 // iOS 标准动画时长
    static let springAnimation = Animation.spring(response: 0.5, dampingFraction: 0.8) // iOS 弹簧动画
    static let hapticFeedback = UIImpactFeedbackGenerator(style: .medium) // iOS 触觉反馈
    
    // iOS 系统集成配置
    static let supportedOrientations: UIInterfaceOrientationMask = .portrait // iOS 支持方向
    static let statusBarStyle: UIStatusBarStyle = .default // iOS 状态栏样式
}
```

### 2. iOS 应用环境配置
```swift
// ✅ iOS 应用环境相关配置
enum Environment {
    case development
    case staging
    case production
    
    static let current: Environment = {
        #if DEBUG
        return .development
        #elseif STAGING
        return .staging
        #else
        return .production
        #endif
    }()
    
    var apiBaseURL: String {
        switch self {
        case .development: return "https://dev-api.lonicera.com"
        case .staging: return "https://staging-api.lonicera.com"
        case .production: return "https://api.lonicera.com"
        }
    }
    
    // iOS 应用特定配置
    var bundleIdentifier: String {
        switch self {
        case .development: return "com.lonicera.app.dev"
        case .staging: return "com.lonicera.app.staging"
        case .production: return "com.lonicera.app"
        }
    }
    
    var isDebugMode: Bool {
        return self == .development
    }
    
    // iOS 日志级别
    var logLevel: OSLogType {
        switch self {
        case .development: return .debug
        case .staging: return .info
        case .production: return .error
        }
    }
}
```

### 3. iOS Swift 禁止重复定义
```swift
// ❌ 禁止在 iOS 应用中重复定义
struct FilterView: View {
    let maxExposure: Float = 2.0 // 重复定义
    let cornerRadius: CGFloat = 12 // 重复定义
}

struct CameraView: View {
    let maxExposure: Float = 2.0 // 重复定义
    let cornerRadius: CGFloat = 12 // 重复定义
}

// ✅ iOS 应用使用配置中心
struct FilterView: View {
    let maxExposure = AppConfig.Camera.maxExposure // 使用统一配置
    
    var body: some View {
        VStack {
            // iOS SwiftUI 视图实现
        }
        .cornerRadius(AppConfig.UI.cornerRadius) // 统一 iOS 样式
    }
}

struct CameraView: View {
    let maxExposure = AppConfig.Camera.maxExposure // 使用统一配置
    
    var body: some View {
        VStack {
            // iOS SwiftUI 视图实现
        }
        .cornerRadius(AppConfig.UI.cornerRadius) // 统一 iOS 样式
    }
}

// ✅ iOS 应用分类配置
extension AppConfig {
    enum Camera {
        static let maxExposure: Float = 2.0 // iOS 相机最大曝光
        static let minExposure: Float = -2.0 // iOS 相机最小曝光
        static let defaultISO: Int = 100 // iOS 相机默认 ISO
    }
    
    enum UI {
        static let cornerRadius: CGFloat = 12 // iOS 统一圆角
        static let shadowRadius: CGFloat = 4 // iOS 统一阴影
        static let animationDuration: Double = 0.3 // iOS 统一动画时长
    }
}
```

---

## 🌏 中文友好支持

### 1. 中文字符处理
```swift
// ✅ 中文字符计数
extension String {
    var chineseCharacterCount: Int { // 中文字符数量
        return self.unicodeScalars.filter { $0.properties.isIdeographic }.count
    }
    
    var displayLength: Int { // 显示长度（中文字符算2个长度）
        return self.reduce(0) { count, char in
            return count + (char.unicodeScalars.first?.properties.isIdeographic == true ? 2 : 1)
        }
    }
}
```

### 2. 中文环境适配
```swift
// ✅ 中文本地化
struct LocalizedConfig {
    static let locale = Locale(identifier: "zh_CN") // 中文环境
    
    static let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.locale = locale
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter
    }()
    
    static let numberFormatter: NumberFormatter = {
        let formatter = NumberFormatter()
        formatter.locale = locale
        formatter.numberStyle = .decimal
        return formatter
    }()
}
```

### 3. 中文输入优化
```swift
// ✅ 中文输入法友好
struct ChineseTextEditor: View {
    @State private var text = ""
    @State private var isComposing = false // 是否正在输入中文
    
    var body: some View {
        TextField("请输入内容", text: $text)
            .onReceive(NotificationCenter.default.publisher(for: UITextInputMode.currentInputModeDidChangeNotification)) { _ in
                // 检测输入法变化
                updateInputMode()
            }
    }
    
    private func updateInputMode() {
        // 处理中文输入法状态
    }
}
```

---

## 🔍 影响评估要求

### 1. 修改前检查清单
```swift
// 修改任何代码前必须检查的项目
struct ImpactAssessment {
    static func checkBeforeModification(target: String) -> [String] {
        return [
            "检查 \(target) 的所有调用者", // 谁在使用这个代码
            "检查 \(target) 的所有依赖", // 这个代码依赖什么
            "检查相关的测试用例", // 是否有测试会受影响
            "检查相关的文档", // 文档是否需要更新
            "检查性能影响", // 是否会影响性能
            "检查内存使用", // 是否会影响内存
            "检查并发安全", // 是否会影响线程安全
        ]
    }
}
```

### 2. 关联功能检查
```swift
// ✅ 修改前的影响分析
func modifyExposureCalculation() {
    // 1. 检查所有使用 exposure 的地方
    // - FilterView.swift
    // - CameraView.swift  
    // - PreviewView.swift
    
    // 2. 检查相关的测试
    // - FilterViewModelTests.swift
    // - ExposureCalculationTests.swift
    
    // 3. 检查相关配置
    // - AppConfig.maxExposure
    // - AppConfig.minExposure
    
    // 4. 执行修改
    // 实际修改代码...
    
    // 5. 验证所有关联功能
    // 运行相关测试，确保功能正常
}
```

---

## 📚 文档同步要求

### 1. README 更新标准
```markdown
<!-- Copyright (c) 2025 LoniceraLab. All rights reserved. -->

# LoniceraLab 项目

## 最新更新 (2025-01-XX)
- ✅ 新增滤镜调整功能
- ✅ 优化图片处理性能
- ✅ 完善中文支持

## 功能说明
### 滤镜功能
- **曝光调整**: 支持 -2.0 到 2.0 范围调整
- **对比度调整**: 支持 -1.0 到 1.0 范围调整
- **使用方法**: 
  ```swift
  let filter = FilterService()
  await filter.setExposure(1.5)
  ```

## 开发规范
- 所有代码文件必须包含 LoniceraLab 版权声明
- 遵循精简主义原则，用最少代码实现功能
- 确保中文环境完美支持
```

### 2. 变更日志格式
```markdown
# 变更日志

## [1.2.0] - 2025-01-XX
### 新增
- 滤镜调整功能模块
- 中文输入法优化支持

### 优化
- 图片处理性能提升 30%
- 内存使用优化，减少 20% 内存占用

### 修复
- 修复中文字符显示问题
- 修复并发访问崩溃问题

### 影响范围
- 影响模块: FilterView, CameraView
- 兼容性: 向后兼容
- 迁移指南: 无需迁移
```

---

## 🎯 质量标准总结

### LoniceraLab 代码质量评分
- **版权合规 (20分)**: 所有文件包含正确版权声明
- **性能效率 (25分)**: 使用高效算法和数据结构
- **代码精简 (20分)**: 用最少代码实现功能
- **规范遵循 (15分)**: 注释、命名、格式符合标准
- **中文支持 (10分)**: 完美支持中文环境
- **文档同步 (10分)**: 文档与代码保持同步

### 达标要求
- **优秀 (90-100分)**: 完全符合 LoniceraLab 标准
- **良好 (80-89分)**: 基本符合标准，有小幅改进空间
- **合格 (70-79分)**: 达到基本要求，需要持续改进
- **不合格 (<70分)**: 不符合标准，需要重新开发

---

## 📱 iOS Swift 开发特色要求

### 1. Swift 语言特性充分利用
```swift
// ✅ 充分利用 Swift 语言特性
enum Result<T> {
    case success(T)
    case failure(Error)
    
    var value: T? { // Swift 计算属性
        if case .success(let value) = self { return value }
        return nil
    }
}

// ✅ Swift 可选链和空合并运算符
let userName = user?.profile?.name ?? "未知用户" // Swift 语法糖

// ✅ Swift 闭包和高阶函数
let validUsers = users.filter { $0.isActive }.map { $0.name } // Swift 函数式编程
```

### 2. iOS 平台集成优化
```swift
// ✅ iOS 系统服务集成
import UserNotifications
import CoreLocation
import AVFoundation

class iOSSystemService {
    // iOS 通知权限
    func requestNotificationPermission() async -> Bool {
        let center = UNUserNotificationCenter.current()
        do {
            return try await center.requestAuthorization(options: [.alert, .sound, .badge])
        } catch {
            return false
        }
    }
    
    // iOS 位置权限
    func requestLocationPermission() {
        let manager = CLLocationManager()
        manager.requestWhenInUseAuthorization()
    }
}
```

### 3. SwiftUI 最佳实践
```swift
// ✅ SwiftUI iOS 应用最佳实践
struct ContentView: View {
    @StateObject private var viewModel = ContentViewModel() // SwiftUI 状态管理
    @Environment(\.scenePhase) private var scenePhase // iOS 生命周期
    
    var body: some View {
        NavigationView {
            VStack {
                // iOS SwiftUI 视图内容
            }
            .navigationTitle("LoniceraLab") // iOS 导航标题
            .navigationBarTitleDisplayMode(.large) // iOS 大标题样式
        }
        .onChange(of: scenePhase) { phase in
            // iOS 应用状态变化处理
            switch phase {
            case .active:
                viewModel.onAppActive()
            case .background:
                viewModel.onAppBackground()
            default:
                break
            }
        }
    }
}
```

### 4. iOS 性能监控
```swift
// ✅ iOS 应用性能监控
import os.log

class iOSPerformanceMonitor {
    private let logger = Logger(subsystem: "com.lonicera.app", category: "Performance")
    
    func measureExecutionTime<T>(operation: () throws -> T) rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try operation()
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        logger.info("操作耗时: \(timeElapsed * 1000, privacy: .public) ms") // iOS 日志
        return result
    }
}
```

---

**记住**: LoniceraLab 追求的是高效、精简、中文友好的现代 iOS Swift 应用开发标准。每一行 Swift 代码都应该体现 iOS 平台的特色和这些价值观。
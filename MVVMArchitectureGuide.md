# 🏗️ Lomo 项目 MVVM-S 架构指南

## 📋 架构概述

基于深入的架构分析和专业评审，制定以下严格的 MVVM-S (Service) 架构指南。所有后续模块重构必须严格按照此指南执行，不允许任何妥协或例外。

## 🎯 架构哲学

**核心理念**: 通过强制依赖注入和清晰的层次分离，构建高度可测试、可维护的现代化 Swift 应用架构。

**设计原则**:
- **单一职责**: 每层只负责自己的核心职责
- **依赖倒置**: 高层模块不依赖低层模块，都依赖抽象
- **开闭原则**: 对扩展开放，对修改封闭
- **可测试性**: 所有业务逻辑都可以独立测试

## 🏛️ 核心架构原则

### 1. 四层架构分离 (100% 强制执行)

```
┌─────────────────┐
│      View       │ ← 纯UI展示，无业务逻辑
├─────────────────┤
│   ViewModel     │ ← 状态管理，业务逻辑
├─────────────────┤
│    Service      │ ← 数据操作，网络请求
├─────────────────┤
│     Model       │ ← 数据结构，业务实体
└─────────────────┘
```

### 2. 依赖注入原则 (零容忍业务单例)

- **强制要求**: 所有 Manager/ViewModel 必须使用依赖注入
- **禁止**: 任何形式的业务逻辑单例 (如 `Manager.shared`)
- **例外**: 仅允许 `DependencyContainer.shared` 作为顶层依赖管理单例
- **构造函数**: 必须接受依赖参数，不允许默认值回退到单例

### 3. DependencyContainer 设计原则

**重要说明**: `DependencyContainer.shared` 是唯一被允许的单例，它的存在是为了：
- 统一管理所有依赖的生命周期
- 简化依赖注入的复杂性
- 消除所有业务逻辑单例

**使用规则**:
- ✅ 在 View 创建时使用 DependencyContainer
- ✅ 在路由/协调器中使用 DependencyContainer
- ❌ 在 ViewModel/Service 中直接访问 DependencyContainer
- ❌ 在业务逻辑中使用 DependencyContainer

## 📊 MVVM 合规评分系统 (100分制)

| 评分项目 | 权重 | 评分标准 |
|---------|------|----------|
| **View层业务逻辑访问** | 25分 | View不直接访问FilterStateManager/业务逻辑 |
| **ViewModel状态管理** | 25分 | 集中状态管理，@Published属性，单一数据源 |
| **Service层数据操作** | 25分 | 数据持久化、网络请求在Service层 |
| **依赖注入模式** | 15分 | 完全消除业务单例，强制依赖注入 |
| **Combine框架使用** | 10分 | @Published属性，响应式编程 |

**目标**: 所有模块必须达到 100/100 分，不接受任何低于100分的实现。

## 🏗️ 标准架构模板

### Model 层
```swift
// ✅ 标准Model实现
struct FilterParameters {
    var exposure: Float = 0.0
    var contrast: Float = 0.0
    var brightness: Float = 0.0
    // 纯数据结构，无业务逻辑
}

// ✅ 业务实体
struct User {
    let id: String
    let name: String
    let isProUser: Bool
}
```

### Service 层
```swift
// ✅ 标准Service协议
protocol FilterServiceProtocol {
    func saveSettings(_ settings: FilterParameters) async throws
    func loadSettings() async throws -> FilterParameters
    func resetToDefaults() async throws
}

// ✅ 标准Service实现
class FilterService: FilterServiceProtocol {
    private let storage: StorageProtocol
    
    init(storage: StorageProtocol) {
        self.storage = storage
    }
    
    func saveSettings(_ settings: FilterParameters) async throws {
        try await storage.save(settings, key: "filter_settings")
    }
    
    func loadSettings() async throws -> FilterParameters {
        return try await storage.load(FilterParameters.self, key: "filter_settings")
    }
    
    func resetToDefaults() async throws {
        let defaults = FilterParameters()
        try await saveSettings(defaults)
    }
}
```

### ViewModel 层
```swift
// ✅ 标准ViewModel实现
class FilterViewModel: ObservableObject {
    // MARK: - 依赖注入 (强制)
    private let filterService: FilterServiceProtocol
    private weak var filterStateManager: FilterStateManager?
    
    // MARK: - 状态管理 (@Published)
    @Published var exposure: Float = 0.0
    @Published var contrast: Float = 0.0
    @Published var brightness: Float = 0.0
    
    // MARK: - UI状态
    @Published var isLoading: Bool = false
    @Published var errorMessage: String = ""
    @Published var showError: Bool = false
    
    // MARK: - 强制依赖注入构造函数
    init(filterService: FilterServiceProtocol, 
         filterStateManager: FilterStateManager) {
        self.filterService = filterService
        self.filterStateManager = filterStateManager
        loadSettings()
    }
    
    // MARK: - 业务逻辑方法
    func updateExposure(_ value: Float) {
        exposure = value
        filterStateManager?.updateParameter(\.exposure, value: value)
        saveSettings()
    }
    
    func resetAllParameters() {
        Task {
            do {
                isLoading = true
                try await filterService.resetToDefaults()
                await loadSettings()
            } catch {
                await handleError(error)
            }
        }
    }
    
    // MARK: - 私有方法
    private func loadSettings() {
        Task {
            do {
                isLoading = true
                let settings = try await filterService.loadSettings()
                await MainActor.run {
                    self.exposure = settings.exposure
                    self.contrast = settings.contrast
                    self.brightness = settings.brightness
                    self.isLoading = false
                }
            } catch {
                await handleError(error)
            }
        }
    }
    
    private func saveSettings() {
        Task {
            do {
                let settings = FilterParameters(
                    exposure: exposure,
                    contrast: contrast,
                    brightness: brightness
                )
                try await filterService.saveSettings(settings)
            } catch {
                await handleError(error)
            }
        }
    }
    
    @MainActor
    private func handleError(_ error: Error) {
        isLoading = false
        errorMessage = error.localizedDescription
        showError = true
        print("❌ [FilterViewModel] 错误: \(error)")
    }
}
```

### View 层
```swift
// ✅ 标准View实现
struct FilterView: View {
    @StateObject private var viewModel: FilterViewModel
    
    init(filterService: FilterServiceProtocol, 
         filterStateManager: FilterStateManager) {
        self._viewModel = StateObject(wrappedValue: 
            FilterViewModel(filterService: filterService, 
                          filterStateManager: filterStateManager))
    }
    
    var body: some View {
        VStack(spacing: 20) {
            // 纯UI展示，通过viewModel访问数据
            VStack {
                Text("曝光: \(viewModel.exposure, specifier: "%.1f")")
                Slider(value: $viewModel.exposure, in: -2...2) { _ in
                    viewModel.updateExposure(viewModel.exposure)
                }
            }
            
            VStack {
                Text("对比度: \(viewModel.contrast, specifier: "%.1f")")
                Slider(value: $viewModel.contrast, in: -1...1)
            }
            
            Button("重置所有参数") {
                viewModel.resetAllParameters()
            }
            .disabled(viewModel.isLoading)
            
            if viewModel.isLoading {
                ProgressView("加载中...")
            }
        }
        .alert("错误", isPresented: $viewModel.showError) {
            Button("确定") { }
        } message: {
            Text(viewModel.errorMessage)
        }
    }
}
```

## 🚫 严格禁止的反模式

### 1. 业务逻辑单例模式 (零容忍)
```swift
// ❌ 绝对禁止
@ObservedObject private var manager = SomeManager.shared
class SomeManager {
    static let shared = SomeManager()
    // 业务逻辑单例
}

// ✅ 强制要求
@StateObject private var manager: SomeManager = {
    SomeManager(dependency: injectedDependency)
}()
```

### 2. View层直接访问业务逻辑
```swift
// ❌ 绝对禁止
@ObservedObject private var filterStateManager = FilterStateManager.shared
Button("重置") {
    filterStateManager.resetAllParameters()
}

// ✅ 强制要求
// View只能通过ViewModel访问数据
Button("重置") {
    viewModel.resetAllParameters()
}
```

### 3. 混合状态管理
```swift
// ❌ 绝对禁止
@State private var localExposure = 0.0
@ObservedObject var filterManager: FilterManager

// ✅ 强制要求
// 所有状态都在ViewModel中
@StateObject private var viewModel: FilterViewModel
```

### 4. ViewModel中的UI逻辑
```swift
// ❌ 绝对禁止
class SomeViewModel: ObservableObject {
    func updateUI() {
        // 直接操作UI
    }
}

// ✅ 强制要求
// ViewModel只管理状态，UI通过数据绑定自动更新
class SomeViewModel: ObservableObject {
    @Published var isVisible: Bool = false
    // UI通过观察isVisible自动更新
}
```

## 🏭 依赖注入容器

### DependencyContainer 完整实现
```swift
// ✅ 标准依赖容器实现
class DependencyContainer {
    static let shared = DependencyContainer()

    // MARK: - 核心依赖
    lazy var filterStateManager: FilterStateManager = FilterStateManager()
    lazy var subscriptionManager: SubscriptionManager = SubscriptionManager()

    // MARK: - Service层
    lazy var filterService: FilterServiceProtocol = FilterService(
        storage: storageService
    )
    lazy var subscriptionService: SubscriptionServiceProtocol = SubscriptionService(
        storage: storageService
    )
    lazy var storageService: StorageProtocol = UserDefaultsStorage()

    // MARK: - ViewModel工厂方法
    func makeFilterViewModel() -> FilterViewModel {
        FilterViewModel(
            filterService: filterService,
            filterStateManager: filterStateManager
        )
    }

    func makeSubscriptionViewModel() -> SubscriptionViewModel {
        SubscriptionViewModel(
            subscriptionService: subscriptionService
        )
    }

    func makeEditViewModel() -> EditViewModel {
        EditViewModel(
            filterStateManager: filterStateManager,
            filterService: filterService
        )
    }

    private init() {
        print("🏭 [DependencyContainer] 初始化完成")
    }
}
```

### View中使用DependencyContainer
```swift
// ✅ 在View创建时使用容器
struct FilterView: View {
    @StateObject private var viewModel = DependencyContainer.shared.makeFilterViewModel()

    var body: some View {
        // UI实现
    }
}

// ✅ 在路由中使用容器
struct AppRouter {
    func makeFilterView() -> FilterView {
        FilterView(viewModel: DependencyContainer.shared.makeFilterViewModel())
    }
}
```

## 🔧 错误处理标准

### ViewModel错误处理模板
```swift
class SomeViewModel: ObservableObject {
    // MARK: - 错误状态
    @Published var isLoading: Bool = false
    @Published var errorMessage: String = ""
    @Published var showError: Bool = false

    // MARK: - 错误处理
    @MainActor
    private func handleError(_ error: Error) {
        isLoading = false
        errorMessage = error.localizedDescription
        showError = true
        print("❌ [SomeViewModel] 错误: \(error)")
    }

    // MARK: - 异步操作模板
    func performAsyncOperation() {
        Task {
            do {
                isLoading = true
                // 执行异步操作
                try await someService.performOperation()
                await MainActor.run {
                    isLoading = false
                }
            } catch {
                await handleError(error)
            }
        }
    }
}
```

### View中错误显示
```swift
struct SomeView: View {
    @StateObject private var viewModel: SomeViewModel

    var body: some View {
        VStack {
            // 主要内容
            if viewModel.isLoading {
                ProgressView("加载中...")
            }
        }
        .alert("错误", isPresented: $viewModel.showError) {
            Button("确定") { }
        } message: {
            Text(viewModel.errorMessage)
        }
    }
}
```

## ✅ 重构检查清单

### 每个模块重构前必须检查:

- [ ] **依赖注入**: 是否完全消除业务单例依赖？
- [ ] **状态管理**: 是否所有状态都在ViewModel中？
- [ ] **View纯化**: View是否只负责UI展示？
- [ ] **Service分离**: 数据操作是否在Service层？
- [ ] **错误处理**: 是否有完整的错误状态管理？
- [ ] **Combine使用**: 是否使用@Published属性？
- [ ] **协议抽象**: Service是否定义了协议接口？

### 重构完成后必须验证:

- [ ] **功能完整性**: 100% 功能保持不变
- [ ] **架构评分**: 达到 100/100 分
- [ ] **代码质量**: 无警告，无重复代码
- [ ] **测试覆盖**: 关键业务逻辑有单元测试
- [ ] **依赖图清晰**: 依赖关系清晰可追踪

## 📋 模块重构优先级

基于架构审查报告，按以下优先级重构:

### 优先级 1 (紧急修复)
1. **Edit模块**: CurveManager单例问题 → 目标100/100分
2. **Settings模块**: 提升至100/100分
3. **Subscription模块**: 消除单例依赖 → 目标100/100分

### 优先级 2 (标准化)
4. **Camera模块**: 统一架构模式 → 目标100/100分
5. **Album模块**: 完善依赖注入 → 目标100/100分

## 🛡️ 架构守护规则

### 代码审查强制要求:
1. **新增Manager**: 必须使用依赖注入模式
2. **状态管理**: 必须使用@Published属性
3. **错误处理**: 必须包含完整错误状态
4. **单例检查**: 严禁任何新的业务逻辑单例
5. **协议设计**: Service层必须定义协议接口

### 违规处理:
- 发现业务单例模式: **立即拒绝**
- View层业务逻辑: **立即拒绝**
- 缺少依赖注入: **立即拒绝**
- 缺少错误处理: **立即拒绝**

### 架构质量保证:
- 每个模块重构后必须达到100/100分
- 所有重构必须保持100%功能完整性
- 必须创建git备份点before/after重构

---

## 🎯 总结

这份 MVVM-S 架构指南确立了严格的架构标准，通过：

1. **清晰的层次分离**: Model-Service-ViewModel-View
2. **强制依赖注入**: 消除业务逻辑单例
3. **统一的DependencyContainer**: 顶层依赖管理
4. **标准化模板**: 可复制的实现模式
5. **量化评分系统**: 客观的质量标准

**执行原则**: 严格按照指南执行，不允许任何妥协或例外，确保每个模块都达到100/100的架构合规分数。

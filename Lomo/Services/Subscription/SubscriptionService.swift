import SwiftUI
import Combine

/// 订阅管理器 - 负责统一管理应用中的订阅相关功能
class SubscriptionService: ObservableObject {
    // 单例实例
    static let shared = SubscriptionService()
    
    // MARK: - 发布属性
    
    // 控制订阅页面显示状态
    @Published var showProView: Bool = false
    
    // 用户订阅状态
    @Published var isProUser: Bool = false
    
    // 私有初始化方法，确保单例
    private init() {
        // 从用户默认设置中加载Pro用户状态
        isProUser = UserDefaults.standard.bool(forKey: "isProUser")
        
        // 订阅通知，监听Pro用户状态变化
        NotificationCenter.default.addObserver(
            forName: Notification.Name("ProUserStatusChanged"),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let isProUser = notification.userInfo?["isProUser"] as? Bool {
                self?.isProUser = isProUser
                // 保存到UserDefaults
                UserDefaults.standard.set(isProUser, forKey: "isProUser")
            }
        }
    }
    
    // MARK: - 公共方法
    
    /// 显示Pro订阅页面
    func showProSubscription() {
        // 无论用户是否为Pro用户，都显示订阅页面
        showProView = true
    }
    
    /// 隐藏Pro订阅页面
    func hideProSubscription() {
        showProView = false
    }
    
    /// 处理Pro功能访问 - 非Pro用户点击Pro功能时调用
    /// - Returns: 如果用户是Pro用户返回true，否则显示订阅页面并返回false
    func handleProFeatureAccess() -> Bool {
        // 如果是Pro用户，允许访问功能
        if isProUser {
            return true
        }
        
        // 如果不是Pro用户，显示订阅页面
        showProView = true
        return false
    }
    
    /// 处理购买特定订阅计划
    func purchase(plan: SubscriptionPlan, completion: @escaping (Result<Void, Error>) -> Void) {
        // 这里应该连接到实际的IAP功能
        // 目前只是模拟购买成功
        print("用户选择了订阅计划: \(plan.rawValue)")
        
        // 模拟购买成功
        self.isProUser = true
        UserDefaults.standard.set(true, forKey: "isProUser")
        
        // 通知其他组件Pro用户状态已变更
        NotificationCenter.default.post(
            name: Notification.Name("ProUserStatusChanged"),
            object: nil,
            userInfo: ["isProUser": true]
        )
        
        // 关闭订阅页面
        self.showProView = false
        
        // 调用完成回调，报告成功
        completion(.success(()))
    }
    
    /// 处理订阅购买
    func handleSubscription(plan: String) {
        // 这里应该连接到实际的IAP功能
        // 目前只是模拟购买成功
        print("用户选择了订阅计划: \(plan)")
        
        // 模拟购买成功
        self.isProUser = true
        UserDefaults.standard.set(true, forKey: "isProUser")
        
        // 通知其他组件Pro用户状态已变更
        NotificationCenter.default.post(
            name: Notification.Name("ProUserStatusChanged"),
            object: nil,
            userInfo: ["isProUser": true]
        )
        
        // 关闭订阅页面
        self.showProView = false
    }
    
    /// 恢复购买
    func restorePurchases() {
        // 连接到IAP恢复购买功能
        print("恢复购买")
    }
}
import Foundation
import Combine

/// 订阅服务协议 - MVVM-S架构
/// 定义订阅相关的所有业务操作接口，完全基于现有SubscriptionService的方法
protocol SubscriptionServiceProtocol: ObservableObject {
    
    // MARK: - 发布属性
    
    /// 控制订阅页面显示状态
    var showProView: Bool { get set }
    
    /// 用户订阅状态
    var isProUser: Bool { get set }
    
    // MARK: - 业务方法
    
    /// 显示Pro订阅页面
    func showProSubscription()
    
    /// 隐藏Pro订阅页面
    func hideProSubscription()
    
    /// 处理Pro功能访问
    /// - Returns: 如果用户是Pro用户返回true，否则显示订阅页面并返回false
    func handleProFeatureAccess() -> Bool
    
    /// 处理购买特定订阅计划
    /// - Parameters:
    ///   - plan: 订阅计划
    ///   - completion: 完成回调
    func purchase(plan: SubscriptionPlan, completion: @escaping (Result<Void, Error>) -> Void)
    
    /// 处理订阅购买
    /// - Parameter plan: 订阅计划字符串
    func handleSubscription(plan: String)
    
    /// 恢复购买
    func restorePurchases()
}
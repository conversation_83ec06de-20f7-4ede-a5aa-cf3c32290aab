import Foundation
import SwiftUI
import Combine

// MARK: - 订阅视图模型
// 负责订阅相关的UI状态管理和业务逻辑协调
@MainActor
class SubscriptionViewModel: ObservableObject {
    // 状态
    @ObservedObject var subscriptionService = SubscriptionService.shared
    // 默认状态下没有选中项
    @Published var selectedPlan: SubscriptionPlan? = nil
    
    // 轮播控制
    @Published var autoScrollTimer: Timer?
    @Published var currentPage = 0
    @Published var userInteracted = false
    @Published var restartTimer: Timer?
    
    // 弹窗控制
    @Published var showAlreadySubscribedAlert = false
    
    // 屏幕尺寸
    let screenWidth = UIScreen.main.bounds.width
    let screenHeight = UIScreen.main.bounds.height
    
    // 订阅计划
    enum SubscriptionPlan: String {
        case monthly = "monthly"
        case yearly = "yearly"
        case lifetime = "lifetime"
    }
    
    // 定义复制多少个卡片到两端以实现循环效果
    let bufferCount = 3
    
    // 功能特性卡片数据
    let featureCards: [FeatureCardModel] = [
        FeatureCardModel(id: 1, title: "滤镜或胶片模拟", description: "专业滤镜库，胶片复刻", backgroundColor: Color.black),
        FeatureCardModel(id: 2, title: "批处理图片", description: "一键处理多张照片", backgroundColor: Color.black),
        FeatureCardModel(id: 3, title: "摄影师签名", description: "添加专业签名和水印", backgroundColor: Color.black),
        FeatureCardModel(id: 4, title: "RAW格式支持", description: "支持编辑和处理RAW格式文件", backgroundColor: Color.black),
        FeatureCardModel(id: 5, title: "高级曲线调整", description: "精确控制色调和颜色曲线", backgroundColor: Color.black),
        FeatureCardModel(id: 6, title: "定制预设", description: "创建和保存您自己的编辑预设", backgroundColor: Color.black),
        FeatureCardModel(id: 7, title: "批量导出", description: "多种格式批量导出，高效处理", backgroundColor: Color.black),
        FeatureCardModel(id: 8, title: "无水印输出", description: "移除所有作品水印，纯净输出", backgroundColor: Color.black),
    ]
    
    // 计算属性：生成用于无限滚动的卡片数据
    var infiniteFeatureCards: [FeatureCardModel] {
        guard !featureCards.isEmpty else { return [] }
        let prefix = featureCards.suffix(bufferCount)
        let suffix = featureCards.prefix(bufferCount)
        return prefix + featureCards + suffix
    }
    
    // 追踪当前居中卡片的索引
    @Published var currentCardIndex: Int? // = 3 // Initial index set in onAppear
    
    @Published var showPrivacyPolicy = false
    @Published var showTermsOfService = false
    
    // 停止自动轮播
    func stopAutoScroll() {
        autoScrollTimer?.invalidate()
        autoScrollTimer = nil
    }
    
    // 处理用户交互后的自动轮播重启
    func handleUserInteraction() {
        // 标记用户交互
        userInteracted = true
        
        // 停止当前自动轮播
        stopAutoScroll()
        
        // 取消已有的重启计时器
        restartTimer?.invalidate()
        
        // 3秒后重新开始自动轮播
        restartTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
            self.startAutoScroll()
            self.userInteracted = false
        }
    }
    
    // 开始自动轮播
    func startAutoScroll() {
        // 实现自动轮播逻辑
    }
    
    // 处理套餐选择和购买逻辑
    func handlePlanSelection(_ plan: SubscriptionPlan) {
        // 1. Visually select the plan immediately
        selectedPlan = plan
        
        // 2. Initiate the purchase process (assuming a method exists with a callback)
        // TODO: Replace with actual purchase call and result handling from SubscriptionService
        print("Initiating purchase for: \(plan.rawValue)")
        // Example placeholder for purchase call with callback:
        /*
        subscriptionManager.purchase(plan: plan) { result in
            // 3. Handle the result (ensure UI updates on main thread)
            DispatchQueue.main.async {
                switch result {
                case .success:
                    // Keep the plan selected if purchase succeeds
                    print("Purchase successful for \(plan.rawValue)")
                    // Optionally dismiss the view or update UI further
                    // dismiss()
                case .failure(let error):
                    // Purchase failed or was cancelled
                    print("Purchase failed/cancelled for \(plan.rawValue): \(error.localizedDescription)")
                    // Only deselect if the *currently* selected plan is the one that failed
                    if self.selectedPlan == plan {
                         self.selectedPlan = nil // Revert selection
                    }
                }
            }
        }
        */
        
        // --- Temporary logic for demonstration --- 
        // Simulating cancellation after 2 seconds for testing UI revert
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
             if self.selectedPlan == plan { // Check if it's still the selected one
                  print("Simulating purchase cancellation for \(plan.rawValue)")
                  self.selectedPlan = nil // Deselect if cancelled
             }
        }
        // --- End temporary logic ---
    }
    
    func handleInfiniteScrollJump(proxy: ScrollViewProxy, newValue: Int?) {
        guard let index = newValue else { return }
        // 实现无限滚动跳转逻辑
    }
}
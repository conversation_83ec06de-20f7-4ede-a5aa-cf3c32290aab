import SwiftUI

struct SubscriptionView: View {
    // 状态
    @Environment(\.dismiss) private var dismiss
    @StateObject private var subscriptionManager = SubscriptionService.shared
    // 默认状态下没有选中项
    @State private var selectedPlan: SubscriptionPlan? = nil
    
    // 轮播控制
    @State private var autoScrollTimer: Timer?
    @State private var currentPage = 0
    @State private var userInteracted = false
    @State private var restartTimer: Timer?
    
    // 弹窗控制
    @State private var showAlreadySubscribedAlert = false
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 定义复制多少个卡片到两端以实现循环效果
    private let bufferCount = 3
    
    // 功能特性卡片数据
    private let featureCards: [FeatureCardModel] = [
        FeatureCardModel(id: 1, title: "滤镜或胶片模拟", description: "专业滤镜库，胶片复刻", backgroundColor: Color.black),
        FeatureCardModel(id: 2, title: "批处理图片", description: "一键处理多张照片", backgroundColor: Color.black),
        FeatureCardModel(id: 3, title: "摄影师签名", description: "添加专业签名和水印", backgroundColor: Color.black),
        FeatureCardModel(id: 4, title: "RAW格式支持", description: "支持编辑和处理RAW格式文件", backgroundColor: Color.black),
        FeatureCardModel(id: 5, title: "高级曲线调整", description: "精确控制色调和颜色曲线", backgroundColor: Color.black),
        FeatureCardModel(id: 6, title: "定制预设", description: "创建和保存您自己的编辑预设", backgroundColor: Color.black),
        FeatureCardModel(id: 7, title: "批量导出", description: "多种格式批量导出，高效处理", backgroundColor: Color.black),
        FeatureCardModel(id: 8, title: "无水印输出", description: "移除所有作品水印，纯净输出", backgroundColor: Color.black),
    ]
    
    // 计算属性：生成用于无限滚动的卡片数据
    private var infiniteFeatureCards: [FeatureCardModel] {
        guard !featureCards.isEmpty else { return [] }
        let prefix = featureCards.suffix(bufferCount)
        let suffix = featureCards.prefix(bufferCount)
        return prefix + featureCards + suffix
    }
    
    // 追踪当前居中卡片的索引
    @State private var currentCardIndex: Int? // = 3 // Initial index set in onAppear
    
    @State private var showPrivacyPolicy = false
    @State private var showTermsOfService = false
    

   
    var body: some View {
        NavigationStack {
            ZStack {
                // 背景色 - 改为与设置页面一致
                Color(uiColor: .systemGray6)
                    .edgesIgnoringSafeArea(.all)
                
                // 内容部分使用ScrollView，可以滚动
                ScrollView {
                    VStack(spacing: 0) {
                        // 功能展示区域 - 使用自定义PageViewController实现无限循环滚动
                        InfinitePageView(pages: featureCards, currentPageIndex: $currentPage, onUserInteraction: handleUserInteraction) { card in
                            FeatureCard(card: card, screenWidth: screenWidth, screenHeight: screenHeight)
                                // 设置卡片宽度为屏幕宽度的80%
                                .frame(width: screenWidth * 0.8)
                        }
                        // 将整体高度调整为屏幕高度的30%
                        .frame(height: screenHeight * 0.3)
                        // 裁剪超出边界的部分
                        .clipped()
                        .padding(.top, screenHeight * 0.02) // 添加顶部内间距2%屏幕高度
                        
                        // 订阅选项
                        VStack(spacing: screenHeight * 0.02) {
                            // 标题
                            Text(subscriptionManager.isProUser ? "已解锁所有专业功能" : "解锁所有专业功能")
                                .font(.system(size: screenHeight * 0.022, weight: .semibold))
                            
                            // 订阅选项 - 改为水平排列
                            HStack(spacing: screenWidth * 0.02) {
                                // 年订阅选项
                                SubscriptionOptionButtonHorizontal(
                                    title: "连续包年",
                                    price: "¥68",
                                    isSelected: selectedPlan == .yearly,
                                    discount: "节省 53%",
                                    badge: "3天试用",
                                    badgeColor: UIConstants.dialIndicatorColor,
                                    action: { 
                                        if subscriptionManager.isProUser {
                                            showAlreadySubscribedAlert = true
                                        } else {
                                            handlePlanSelection(.yearly) 
                                        }
                                    }
                                )
                                .frame(maxWidth: .infinity) // 确保等宽
                                
                                // 月订阅选项
                                SubscriptionOptionButtonHorizontal(
                                    title: "连续包月",
                                    price: "¥8",
                                    isSelected: selectedPlan == .monthly,
                                    discount: "¥12",  // 添加删除线价格
                                    badge: "限时优惠",  // 添加限时优惠标签
                                    badgeColor: UIConstants.dialIndicatorColor,
                                    action: { 
                                        if subscriptionManager.isProUser {
                                            showAlreadySubscribedAlert = true
                                        } else {
                                            handlePlanSelection(.monthly) 
                                        }
                                    }
                                )
                                .frame(maxWidth: .infinity) // 确保等宽
                                
                                // 一个月选项
                                SubscriptionOptionButtonHorizontal(
                                    title: "一个月",
                                    price: "¥12",
                                    isSelected: selectedPlan == .lifetime,
                                    discount: nil,
                                    badge: nil,
                                    badgeColor: UIConstants.dialIndicatorColor,
                                    action: { 
                                        if subscriptionManager.isProUser {
                                            showAlreadySubscribedAlert = true
                                        } else {
                                            handlePlanSelection(.lifetime) 
                                        }
                                    }
                                )
                                .frame(maxWidth: .infinity) // 确保等宽
                            }
                            .padding(.horizontal, screenWidth * 0.04)
                        }
                        .padding(.top, screenHeight * 0.03)
                        
                        // 底部说明和链接
                        VStack(spacing: screenHeight * 0.01) {  // 内部元素间距改为1%
                            // 自动续费说明
                            (Text("自动续费，可随时取消:")
                                .fontWeight(.bold) // 将这部分设为粗体
                             +
                             Text(" 确认购买后，您的iTunes账户将被收取费用，订阅将会自动续订，除非在当前订阅结束前至少提前24小时关闭自动续订。您的账户将在当前订阅结束前的最后24小时里被收取续订费用，并确定续订开支。具体路径是: \"设置\"-> Apple ID -> 媒体与购买项目 -> 订阅")
                            )
                                .font(.system(size: screenHeight * 0.013))
                                .foregroundColor(.gray)
                                .multilineTextAlignment(.leading)
                                .padding(.horizontal, screenWidth * 0.08)
                            
                            // 政策链接
                            HStack(spacing: screenWidth * 0.08) {
                                NavigationLink {
                                    PrivacyPolicyView()
                                } label: {
                                    Text("隐私政策")
                                        .font(.system(size: screenHeight * 0.014))
                                        .foregroundColor(.gray)
                                }
                                
                                Button(action: {
                                    subscriptionManager.restorePurchases()
                                }) {
                                    Text("恢复购买")
                                        .font(.system(size: screenHeight * 0.014))
                                        .foregroundColor(.gray)
                                }
                                
                                NavigationLink {
                                    TermsOfServiceView()
                                } label: {
                                    Text("服务条款")
                                        .font(.system(size: screenHeight * 0.014))
                                        .foregroundColor(.gray)
                                }
                            }
                            
                            // 添加工作室LOGO和文本
                            HStack(spacing: screenWidth * 0.02) {
                                // Logo直接使用圆形裁剪
                                Image("LH-LOGO")
                                    .renderingMode(.original)
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: screenHeight * 0.03, height: screenHeight * 0.03)
                                    .clipShape(Circle())
                                
                                Text("LoniceraLab出品")
                                    .font(.system(size: screenHeight * 0.014))
                                    .foregroundColor(.gray)
                            }
                            .padding(.top, screenHeight * 0.03)  // 改为3%的顶部间距
                            
                            // 官方认证
                            HStack(spacing: screenWidth * 0.15) {
                                // 内容已删除，保留结构
                            }
                            .padding(.vertical, screenHeight * 0.02)
                        }
                        .padding(.top, screenHeight * 0.02)  // 增加顶部2%内边距
                    }
                }
                .background(Color(uiColor: .systemGray6)) // 给ScrollView背景色，避免透明 - 改为与设置页面一致
            }
            .navigationBarTitleDisplayMode(.inline) // 使用 inline 模式
            .toolbar {
                // 中间 LOMO 标志和 PRO 标签
                ToolbarItemGroup(placement: .principal) {
                    HStack(alignment: .bottom, spacing: screenWidth * 0.01) {
                        Image("LH Lomo")
                            .renderingMode(.template)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .foregroundColor(.white)
                            .frame(height: screenHeight * 0.025)
                        
                        // 使用导航栏专用的Pro标签，并传入实际的Pro用户状态
                        NavProLabel(screenHeight: screenHeight, isProUser: subscriptionManager.isProUser)
                    }
                }
                // 右侧关闭按钮
                ToolbarItemGroup(placement: .navigationBarTrailing) {
                    Button(action: {
                        dismiss()
                        subscriptionManager.hideProSubscription()
                    }) {
                        Image(systemName: "xmark") // 确认使用 xmark
                            .font(.system(size: screenHeight * 0.02, weight: .semibold)) // 改为2%大小，中黑体
                            .foregroundColor(.gray)
                    }
                }
            }
        }
        .preferredColorScheme(.dark)
        .onAppear {
            // 初始化自动轮播计时器
            startAutoScroll()
        }
        .onDisappear {
            // 停止轮播
            stopAutoScroll()
        }
        .alert("已订阅", isPresented: $showAlreadySubscribedAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text("您已经是Pro用户，已解锁所有专业功能")
        }
    } 
   
    // 启动自动轮播
    private func startAutoScroll() {
        // 如果已存在计时器，先停止
        stopAutoScroll()
        
        autoScrollTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { _ in
            // 切换到下一页，始终向右循环滚动
            withAnimation {
                // 更新当前页面索引
                currentPage = (currentPage + 1) % featureCards.count
            }
        }
    }
    
    // 停止自动轮播
    private func stopAutoScroll() {
        autoScrollTimer?.invalidate()
        autoScrollTimer = nil
    }
    
    // 处理用户交互后的自动轮播重启
    private func handleUserInteraction() {
        // 标记用户交互
        userInteracted = true
        
        // 停止当前自动轮播
        stopAutoScroll()
        
        // 取消已有的重启计时器
        restartTimer?.invalidate()
        
        // 3秒后重新开始自动轮播
        restartTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
            self.startAutoScroll()
            self.userInteracted = false
        }
    }
    
    // 处理套餐选择和购买逻辑
    private func handlePlanSelection(_ plan: SubscriptionPlan) {
        // 1. Visually select the plan immediately
        selectedPlan = plan
        
        // 2. Initiate the purchase process (assuming a method exists with a callback)
        // TODO: Replace with actual purchase call and result handling from SubscriptionService
        print("Initiating purchase for: \(plan.rawValue)")
        
        // --- Temporary logic for demonstration --- 
        // Simulating cancellation after 2 seconds for testing UI revert
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
             if self.selectedPlan == plan { // Check if it's still the selected one
                  print("Simulating purchase cancellation for \(plan.rawValue)")
                  self.selectedPlan = nil // Deselect if cancelled
             }
        }
        // --- End temporary logic ---
    }
}

// MARK: - 订阅选项按钮（水平布局）
struct SubscriptionOptionButtonHorizontal: View {
    let title: String
    let price: String
    let isSelected: Bool
    let discount: String?
    let badge: String?
    let badgeColor: Color
    let action: () -> Void
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        VStack(spacing: screenHeight * 0.01) {
            Text(title)
                .font(.system(size: screenHeight * 0.016, weight: .medium))
            
            Text(price)
                .font(.system(size: screenHeight * 0.02, weight: .bold))
            
            // 折扣标签或占位符
            if let discount = discount {
                let isMonthlyStrikethrough = (title == "连续包月")
                
                Text(discount)
                    .font(.system(size: screenHeight * 0.012))
                    // 根据是否为"连续包月"设置不同前景色
                    .foregroundColor(isMonthlyStrikethrough ? .gray : .black)
                    // 仅为"连续包月"的价格添加删除线
                    .strikethrough(isMonthlyStrikethrough)
                    .padding(.horizontal, screenWidth * 0.01)
                    // 保持垂直内边距以确保高度一致
                    .padding(.vertical, screenHeight * 0.002)
                    // 根据是否为"连续包月"设置不同背景和圆角
                    .background(isMonthlyStrikethrough ? Color.clear : UIConstants.dialIndicatorColor)
                    .cornerRadius(isMonthlyStrikethrough ? 0 : UIConstants.smallCornerRadius)
            } else {
                // 占位符也需要保持相同的高度
                Text(" ")
                    .font(.system(size: screenHeight * 0.012))
                    .padding(.vertical, screenHeight * 0.002) // 确保垂直padding一致
                    .foregroundColor(.clear)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, screenHeight * 0.02)
        .background(
            ZStack {
                // 背景填充 - 使用常量颜色并保持透明度
                RoundedRectangle(cornerRadius: UIConstants.largeCornerRadius)
                    .fill(isSelected ? UIConstants.dialIndicatorColor.opacity(0.15) : Color.clear)
                
                // 黄色边框 - 使用常量颜色
                RoundedRectangle(cornerRadius: UIConstants.largeCornerRadius)
                    .strokeBorder(UIConstants.dialIndicatorColor, lineWidth: 1)
                
                // 添加徽章
                if let badge = badge {
                    VStack {
                        HStack {
                            PromotionBadge(text: badge, color: badgeColor, screenWidth: screenWidth, screenHeight: screenHeight)
                                // 向上偏移标签高度的一半 (估算 1.5% / 2 = 0.75%)
                                .offset(y: -screenHeight * 0.0075)
                            
                            Spacer()
                        }
                        Spacer()
                    }
                     // 将徽章放在ZStack的左上角
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                }
            }
        )
        .contentShape(Rectangle())
        .onTapGesture {
            action()
        }
    }
}

// MARK: - 通用促销标签组件
struct PromotionBadge: View {
    let text: String
    let color: Color
    let screenWidth: CGFloat
    let screenHeight: CGFloat
    
    var body: some View {
        Text(text)
            .font(.system(size: screenHeight * 0.01))
            // 文本颜色改为黑色
            .foregroundColor(.black)
            // 水平内边距改为 4%
            .padding(.horizontal, screenWidth * 0.04)
            // 垂直内边距改为 0.25%
            .padding(.vertical, screenHeight * 0.0025)
            // 背景改回纯色
            .background(color)
            // 应用顶部和右下角圆角，左下角直角
            .clipShape(RoundedCorner(radius: UIConstants.largeCornerRadius, corners: [.topLeft, .topRight, .bottomRight]))
    }
}

// Helper shape for specific rounded corners
struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(roundedRect: rect, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        return Path(path.cgPath)
    }
}

// MARK: - 功能展示卡片
struct FeatureCard: View {
    let card: FeatureCardModel
    let screenWidth: CGFloat
    let screenHeight: CGFloat
    
    var body: some View {
        // 恢复原来的卡片样式
        ZStack {
            card.backgroundColor
                .cornerRadius(screenHeight * 0.02)
            
            VStack {
                Spacer()
                
                Text(card.title)
                    .font(.system(size: screenHeight * 0.022, weight: .bold))
                    .foregroundColor(.white)
                    .padding(.bottom, screenHeight * 0.02)
                
                if !card.description.isEmpty {
                    Text(card.description)
                        .font(.system(size: screenHeight * 0.016))
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                        .padding(.bottom, screenHeight * 0.02)
                }
            }
            .padding(.bottom, screenHeight * 0.02)
        }
        // 不需要内部水平内边距，由外部控制
        // .padding(.horizontal, screenWidth * 0.05)
    }
}

// MARK: - 专用于导航栏的Pro标签组件
fileprivate struct NavProLabel: View {
    let screenHeight: CGFloat
    let isProUser: Bool
    
    var body: some View {
        Text("PRO")
            .font(.system(size: screenHeight * 0.01, weight: .medium))
            .foregroundColor(.white)
            .padding(.horizontal, 2)
            .padding(.vertical, 1)
            .background(isProUser ? UIConstants.dialIndicatorColor.opacity(UIConstants.effectOpacity) : Color.gray.opacity(UIConstants.effectOpacity))
            .clipShape(RoundedRectangle(cornerRadius: 2))
    }
}

// MARK: - InfinitePageView - 使用UIPageViewController实现无限循环滚动
struct InfinitePageView<Data, Content>: UIViewControllerRepresentable where Data: RandomAccessCollection, Data.Element: Identifiable, Content: View {
    private let pages: Data
    private let pageContent: (Data.Element) -> Content
    @Binding var currentPageIndex: Int
    var onUserInteraction: (() -> Void)?
    
    init(pages: Data, currentPageIndex: Binding<Int>, onUserInteraction: (() -> Void)? = nil, @ViewBuilder pageContent: @escaping (Data.Element) -> Content) {
        self.pages = pages
        self._currentPageIndex = currentPageIndex
        self.pageContent = pageContent
        self.onUserInteraction = onUserInteraction
    }
    
    func makeUIViewController(context: Context) -> UIPageViewController {
        // 设置页面间距和其他选项
        let options: [UIPageViewController.OptionsKey: Any] = [
            .interPageSpacing: 20.0 // 设置页面之间的间距
        ]
        
        let pageViewController = UIPageViewController(
            transitionStyle: .scroll,
            navigationOrientation: .horizontal,
            options: options
        )
        pageViewController.dataSource = context.coordinator
        pageViewController.delegate = context.coordinator
        
        // 设置初始页面
        if let firstPage = pages.first {
            let hostingController = UIHostingController(rootView: pageContent(firstPage))
            hostingController.view.backgroundColor = .clear
            
            context.coordinator.viewControllers[firstPage.id] = hostingController
            pageViewController.setViewControllers([hostingController], direction: .forward, animated: false)
        }
        
        // 调整UIPageViewController样式
        pageViewController.view.backgroundColor = .clear
        
        // 启用循环滚动
        context.coordinator.enableInfiniteScrolling = true
        
        return pageViewController
    }
    
    func updateUIViewController(_ pageViewController: UIPageViewController, context: Context) {
        // 确保所有页面控制器都使用正确的尺寸
        pageViewController.viewControllers?.forEach { viewController in
            viewController.preferredContentSize = CGSize(
                width: UIScreen.main.bounds.width * 0.5, // 屏幕宽度的50%
                height: UIScreen.main.bounds.height * 0.3 // 屏幕高度的30%
            )
        }
        
        // 检查页面索引是否已更改，并更新当前显示的页面
        if let currentVC = pageViewController.viewControllers?.first,
           let currentIndex = context.coordinator.indexOfViewController(currentVC),
           let targetIndex = pages.indices.first(where: { i in pages.index(pages.startIndex, offsetBy: currentPageIndex) == i }) {
            if currentIndex != targetIndex {
                // 始终使用向前(.forward)方向，实现向右循环效果
                let direction: UIPageViewController.NavigationDirection = .forward
                if let targetPage = pages[safe: targetIndex],
                   let viewController = context.coordinator.getViewController(for: targetPage) {
                    pageViewController.setViewControllers([viewController], direction: direction, animated: true)
                }
            }
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }   
 
    class Coordinator: NSObject, UIPageViewControllerDataSource, UIPageViewControllerDelegate {
        private let parent: InfinitePageView
        var viewControllers: [AnyHashable: UIHostingController<Content>] = [:]
        var enableInfiniteScrolling = true
        private var isUserInitiatedScroll = false
        
        init(_ parent: InfinitePageView) {
            self.parent = parent
            super.init()
        }
        
        // 找到当前显示的页面的索引
        func indexOfViewController(_ viewController: UIViewController) -> Data.Index? {
            if let hostingController = viewController as? UIHostingController<Content>,
               let id = parent.pages.first(where: { viewControllers[$0.id] === hostingController })?.id {
                return parent.pages.firstIndex(where: { $0.id == id })
            }
            return nil
        }
        
        // 获取指定索引的页面
        private func getPage(at index: Data.Index) -> Data.Element? {
            return parent.pages.indices.contains(index) ? parent.pages[index] : nil
        }
        
        // 获取指定索引的视图控制器
        func getViewController(for page: Data.Element) -> UIViewController? {
            if let existingController = viewControllers[page.id] {
                return existingController
            } else {
                let hostingController = UIHostingController(rootView: parent.pageContent(page))
                hostingController.view.backgroundColor = .clear
                
                viewControllers[page.id] = hostingController
                return hostingController
            }
        }
        
        // MARK: - UIPageViewControllerDelegate
        
        // 检测用户何时开始拖拽
        func pageViewController(_ pageViewController: UIPageViewController, willTransitionTo pendingViewControllers: [UIViewController]) {
            isUserInitiatedScroll = true
        }
        
        func pageViewController(_ pageViewController: UIPageViewController, didFinishAnimating finished: Bool, previousViewControllers: [UIViewController], transitionCompleted completed: Bool) {
            if completed,
               let visibleViewController = pageViewController.viewControllers?.first,
               let index = indexOfViewController(visibleViewController) {
                let indexOffset = parent.pages.distance(from: parent.pages.startIndex, to: index)
                parent.currentPageIndex = indexOffset
                
                // 如果是用户发起的滚动，通知父视图重置计时器
                if isUserInitiatedScroll {
                    parent.onUserInteraction?()
                    isUserInitiatedScroll = false
                }
            }
        }
        
        // MARK: - UIPageViewControllerDataSource
        
        func pageViewController(_ pageViewController: UIPageViewController, viewControllerBefore viewController: UIViewController) -> UIViewController? {
            guard let index = indexOfViewController(viewController) else { return nil }
            
            // 实现循环滚动：从第一页可以滚动到最后一页
            if index == parent.pages.startIndex {
                if enableInfiniteScrolling, let lastPage = parent.pages.last {
                    return getViewController(for: lastPage)
                }
                return nil
            }
            
            let previousIndex = parent.pages.index(before: index)
            guard let page = getPage(at: previousIndex) else { return nil }
            return getViewController(for: page)
        }
        
        func pageViewController(_ pageViewController: UIPageViewController, viewControllerAfter viewController: UIViewController) -> UIViewController? {
            guard let index = indexOfViewController(viewController) else { return nil }
            
            // 实现循环滚动：从最后一页可以滚动到第一页
            if index == parent.pages.index(before: parent.pages.endIndex) {
                if enableInfiniteScrolling, let firstPage = parent.pages.first {
                    return getViewController(for: firstPage)
                }
                return nil
            }
            
            let nextIndex = parent.pages.index(after: index)
            guard let page = getPage(at: nextIndex) else { return nil }
            return getViewController(for: page)
        }
    }
}

// MARK: - 扩展RandomAccessCollection，以安全访问元素
extension RandomAccessCollection {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}

// MARK: - 预览
struct SubscriptionView_Previews: PreviewProvider {
    static var previews: some View {
        SubscriptionView()
    }
}